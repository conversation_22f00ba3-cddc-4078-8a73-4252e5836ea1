package me.zivush.grimoireenchant.mana;

import me.zivush.grimoireenchant.GrimoireEnchant;
import me.zivush.grimoireenchant.utils.ColorUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Manages the mana core GUI
 */
public class ManaCoreGUI implements Listener {

    private final GrimoireEnchant plugin;
    private final ManaCoreManager manaCoreManager;
    private final Set<UUID> openGuis = new HashSet<>();

    /**
     * Constructor for ManaCoreGUI
     *
     * @param plugin The main plugin instance
     * @param manaCoreManager The mana core manager
     */
    public ManaCoreGUI(GrimoireEnchant plugin, ManaCoreManager manaCoreManager) {
        this.plugin = plugin;
        this.manaCoreManager = manaCoreManager;

        // Register events
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Open the mana core GUI for a player
     *
     * @param player The player
     */
    public void openGui(Player player) {
        ConfigurationSection config = plugin.getGuiConfig().getConfigurationSection("ManaCoreGUI");
        if (config == null) return;

        String title = ColorUtils.process(config.getString("Title", "Mana Cores"));
        int size = config.getInt("Size", 54);

        Inventory inventory = Bukkit.createInventory(null, size, title);

        // Add border items
        ConfigurationSection borderConfig = config.getConfigurationSection("BorderItem");
        if (borderConfig != null) {
            Material borderMaterial = Material.valueOf(borderConfig.getString("Material", "GRAY_STAINED_GLASS_PANE"));
            String borderName = ColorUtils.process(borderConfig.getString("Name", " "));

            ItemStack borderItem = new ItemStack(borderMaterial);
            ItemMeta borderMeta = borderItem.getItemMeta();
            if (borderMeta != null) {
                borderMeta.setDisplayName(borderName);
                borderItem.setItemMeta(borderMeta);
            }

            for (int slotIndex : borderConfig.getIntegerList("Slots")) {
                if (slotIndex < size) {
                    inventory.setItem(slotIndex, borderItem);
                }
            }
        }

        // Add info item
        ConfigurationSection infoConfig = config.getConfigurationSection("InfoItem");
        if (infoConfig != null) {
            Material infoMaterial = Material.valueOf(infoConfig.getString("Material", "BOOK"));
            String infoName = ColorUtils.process(infoConfig.getString("Name", "Mana Core Info"));
            List<String> infoLore = infoConfig.getStringList("Lore").stream()
                    .map(line -> line.replace("{active_core}", getActiveCoreName(player)))
                    .map(ColorUtils::process)
                    .collect(Collectors.toList());
            int infoSlot = infoConfig.getInt("Slot", 4);

            ItemStack infoItem = new ItemStack(infoMaterial);
            ItemMeta infoMeta = infoItem.getItemMeta();
            if (infoMeta != null) {
                infoMeta.setDisplayName(infoName);
                infoMeta.setLore(infoLore);
                infoItem.setItemMeta(infoMeta);
            }

            inventory.setItem(infoSlot, infoItem);
        }

        // Add active core or no core item
        String activeCore = manaCoreManager.getActiveCore(player);
        if (!activeCore.equals("none")) {
            // Add active core indicator
            ConfigurationSection activeIndicatorConfig = config.getConfigurationSection("ActiveCoreIndicator");
            if (activeIndicatorConfig != null) {
                Material indicatorMaterial = Material.valueOf(activeIndicatorConfig.getString("Material", "LIME_STAINED_GLASS_PANE"));
                String indicatorName = ColorUtils.process(activeIndicatorConfig.getString("Name", "Active"));
                List<String> indicatorLore = activeIndicatorConfig.getStringList("Lore").stream()
                        .map(ColorUtils::process)
                        .collect(Collectors.toList());

                ItemStack indicatorItem = new ItemStack(indicatorMaterial);
                ItemMeta indicatorMeta = indicatorItem.getItemMeta();
                if (indicatorMeta != null) {
                    indicatorMeta.setDisplayName(indicatorName);
                    indicatorMeta.setLore(indicatorLore);
                    indicatorItem.setItemMeta(indicatorMeta);
                }

                // Place the active core in the center
                ItemStack coreItem = manaCoreManager.createCoreItem(activeCore);
                if (coreItem != null) {
                    inventory.setItem(22, coreItem);

                    // Add the active indicator above the core
                    inventory.setItem(13, indicatorItem);
                }
            }
        } else {
            // Add no core item
            ConfigurationSection noCoreConfig = config.getConfigurationSection("NoCoreItem");
            if (noCoreConfig != null) {
                Material noCoreMaterial = Material.valueOf(noCoreConfig.getString("Material", "BARRIER"));
                String noCoreName = ColorUtils.process(noCoreConfig.getString("Name", "No Core Equipped"));
                List<String> noCoreLore = noCoreConfig.getStringList("Lore").stream()
                        .map(ColorUtils::process)
                        .collect(Collectors.toList());
                int noCoreSlot = noCoreConfig.getInt("Slot", 22);

                ItemStack noCoreItem = new ItemStack(noCoreMaterial);
                ItemMeta noCoreMeta = noCoreItem.getItemMeta();
                if (noCoreMeta != null) {
                    noCoreMeta.setDisplayName(noCoreName);
                    noCoreMeta.setLore(noCoreLore);
                    noCoreItem.setItemMeta(noCoreMeta);
                }

                inventory.setItem(noCoreSlot, noCoreItem);
            }
        }

        // Add player's cores
        List<String> playerCores = manaCoreManager.getPlayerCores(player);
        List<Integer> coreSlots = config.getIntegerList("CoreSlots");
        int slotIndex = 0;

        for (String coreId : playerCores) {
            // Skip the active core, it's already displayed
            if (coreId.equals(activeCore)) continue;

            // Create the core item
            ItemStack coreItem = manaCoreManager.createCoreItem(coreId);
            if (coreItem != null && slotIndex < coreSlots.size()) {
                inventory.setItem(coreSlots.get(slotIndex), coreItem);
                slotIndex++;
            }
        }

        // Open the inventory
        player.openInventory(inventory);
        openGuis.add(player.getUniqueId());
    }

    /**
     * Get the display name of the active core
     *
     * @param player The player
     * @return The display name of the active core, or "None" if no core is active
     */
    private String getActiveCoreName(Player player) {
        String activeCore = manaCoreManager.getActiveCore(player);
        if (activeCore.equals("none")) {
            return "&#FF6347ɴᴏɴᴇ";
        }

        ConfigurationSection coreSection = plugin.getConfig().getConfigurationSection("ManaCores." + activeCore);
        if (coreSection == null) {
            return "&#FF6347ɴᴏɴᴇ";
        }

        return coreSection.getString("Name", activeCore);
    }

    /**
     * Handle inventory click events
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;

        Player player = (Player) event.getWhoClicked();
        UUID uuid = player.getUniqueId();

        // Check if this is our GUI
        if (!openGuis.contains(uuid)) return;

        ConfigurationSection config = plugin.getGuiConfig().getConfigurationSection("ManaCoreGUI");
        if (config == null) return;

        String title = ColorUtils.process(config.getString("Title", "Mana Cores"));

        // Check if this is our GUI by title
        if (!event.getView().getTitle().equals(title)) return;

        // Cancel the event to prevent item movement
        event.setCancelled(true);

        // Get the clicked item
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;

        // Check if the clicked item is a core
        String activeCore = manaCoreManager.getActiveCore(player);
        List<String> playerCores = manaCoreManager.getPlayerCores(player);

        // Check if clicked on the active core
        if (event.getSlot() == 22 && !activeCore.equals("none")) {
            // Unequip the core
            manaCoreManager.setActiveCore(player, "none");
            player.sendMessage(ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.Prefix", "") +
                    plugin.getMessagesConfig().getString("ManaCores.Unequipped", "&#FF6347ʏᴏᴜ ʜᴀᴠᴇ ᴜɴᴇǫᴜɪᴘᴘᴇᴅ ʏᴏᴜʀ ᴍᴀɴᴀ ᴄᴏʀᴇ.")));

            // Reopen the GUI
            openGui(player);
            return;
        }

        // Check if clicked on a core in the core slots
        List<Integer> coreSlots = config.getIntegerList("CoreSlots");
        if (coreSlots.contains(event.getSlot())) {
            // Find which core was clicked
            for (String coreId : playerCores) {
                ItemStack coreItem = manaCoreManager.createCoreItem(coreId);
                if (coreItem != null && clickedItem.isSimilar(coreItem)) {
                    // Check if shift-clicked (take back to inventory)
                    if (event.isShiftClick()) {
                        // Remove core from player's collection
                        if (manaCoreManager.removeCore(player, coreId)) {
                            // Create physical core item
                            ItemStack physicalCore = manaCoreManager.createPhysicalCoreItem(coreId);
                            if (physicalCore != null) {
                                // Give the physical core item to the player
                                if (player.getInventory().firstEmpty() == -1) {
                                    // Inventory is full, drop the item
                                    player.getWorld().dropItemNaturally(player.getLocation(), physicalCore);
                                    player.sendMessage(ColorUtils.process(
                                            plugin.getMessagesConfig().getString("General.Prefix", "") +
                                            plugin.getMessagesConfig().getString("ManaCores.InventoryFull", "&#FF6347ʏᴏᴜʀ ɪɴᴠᴇɴᴛᴏʀʏ ɪs ғᴜʟʟ! ᴛʜᴇ ᴄᴏʀᴇ ᴡᴀs ᴅʀᴏᴘᴘᴇᴅ ᴏɴ ᴛʜᴇ ɢʀᴏᴜɴᴅ.")));
                                } else {
                                    player.getInventory().addItem(physicalCore);
                                }

                                player.sendMessage(ColorUtils.process(
                                        plugin.getMessagesConfig().getString("General.Prefix", "") +
                                        plugin.getMessagesConfig().getString("ManaCores.TookBack", "&#4CBB17ʏᴏᴜ ᴛᴏᴏᴋ ᴛʜᴇ &#FFFFFF{core} &#4CBB17ᴍᴀɴᴀ ᴄᴏʀᴇ ʙᴀᴄᴋ ᴛᴏ ʏᴏᴜʀ ɪɴᴠᴇɴᴛᴏʀʏ.")
                                                .replace("{core}", coreId)));

                                // Reopen the GUI
                                openGui(player);
                                return;
                            }
                        }
                    } else {
                        // Regular click - equip the core
                        manaCoreManager.setActiveCore(player, coreId);
                        player.sendMessage(ColorUtils.process(
                                plugin.getMessagesConfig().getString("General.Prefix", "") +
                                plugin.getMessagesConfig().getString("ManaCores.Equipped", "&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ ᴇǫᴜɪᴘᴘᴇᴅ ᴛʜᴇ &#FFFFFF{core} &#4CBB17ᴍᴀɴᴀ ᴄᴏʀᴇ.")
                                        .replace("{core}", coreId)));

                        // Reopen the GUI
                        openGui(player);
                        return;
                    }
                }
            }
        }
    }

    /**
     * Handle inventory close events
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;

        Player player = (Player) event.getPlayer();
        openGuis.remove(player.getUniqueId());
    }
}
